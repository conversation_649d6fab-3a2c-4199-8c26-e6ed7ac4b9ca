package com.prospection.refdata.common

import org.apache.spark.SparkConf
import java.io.File

object SparkConfig {
    private val ENVIRONMENTS = arrayOf("int", "uat", "prd")
    private val BUCKET_COMMONS = mapOf(
        "spark.hadoop.fs.s3a.bucket.pd-au-%s-common.endpoint" to "s3.ap-southeast-2.amazonaws.com",
        "spark.hadoop.fs.s3a.bucket.pd-jp-%s-common.endpoint" to "s3.ap-northeast-1.amazonaws.com",
        "spark.hadoop.fs.s3a.bucket.pd-us-%s-common.endpoint" to "s3.us-west-2.amazonaws.com",
    )

    /*
     * This method is used to get the Spark configuration for the cloud environment.
     */
    fun getCloudSparkConfig(): SparkConf {
        val sparkConfig = getDefaultSparkConfig()
            .set(
                "spark.hadoop.fs.s3a.aws.credentials.provider",
                "software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider"
            )
            .set("spark.hadoop.fs.s3a.endpoint", "s3.ap-southeast-2.amazonaws.com")
            .set(
                "spark.hadoop.fs.s3a.bucket.prospection-data-lake-ap-northeast-1.endpoint",
                "s3.ap-northeast-1.amazonaws.com"
            )
            .set("spark.hadoop.fs.s3a.bucket.prospection-data-lake-us-west-2.endpoint", "s3.us-west-2.amazonaws.com")

        ENVIRONMENTS.forEach { env ->
            BUCKET_COMMONS.forEach { (bucket, region) ->
                sparkConfig.set(String.format(bucket, env), region)
            }
        }

        return sparkConfig
    }

    fun getDefaultSparkConfig(): SparkConf {
        // Define base temporary directory for Spark
        val sparkTempDir = "/tmp/spark-temp"

        // Ensure the temp directory exists
        val tempDir = File(sparkTempDir)
        val created = tempDir.mkdirs()

        println("SparkConfig: Setting up temporary directories...")
        println("SparkConfig: Temp directory: ${tempDir.absolutePath} (created: $created)")

        // Log directory permissions
        logDirectoryInfo(tempDir)

        // Create subdirectories
        val warehouseDir = File("$sparkTempDir/warehouse")
        val checkpointsDir = File("$sparkTempDir/checkpoints")
        warehouseDir.mkdirs()
        checkpointsDir.mkdirs()

        println("SparkConfig: Warehouse directory: ${warehouseDir.absolutePath}")
        println("SparkConfig: Checkpoints directory: ${checkpointsDir.absolutePath}")

        val conf = SparkConf()
            .set("spark.master", "local[*]")
            .set("spark.hadoop.fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem")
            .set("spark.hadoop.fs.s3a.fast.upload", "true")
            .set("spark.hadoop.fs.s3a.fast.upload.buffer", "bytebuffer")
            .set("spark.testing.memory", "471859200")
            .set("spark.hadoop.mapreduce.fileoutputcommitter.cleanup-failures.ignored", "true")
            .set("spark.hadoop.parquet.enable.summary-metadata", "false")
            .set("spark.sql.parquet.mergeSchema", "false")
            .set("spark.sql.parquet.filterPushdown", "true")
            .set("spark.sql.hive.metastorePartitionPruning", "true")
            .set("spark.hadoop.fs.s3a.commiter.staging.conflict-mode", "replace")
            .set("spark.hadoop.fs.s3a.committer.name", "magic")
            .set(
                "spark.sql.sources.commitProtocolClass",
                "org.apache.spark.internal.io.cloud.PathOutputCommitProtocol"
            )
            .set(
                "spark.sql.parquet.output.committer.class",
                "org.apache.spark.internal.io.cloud.BindingParquetOutputCommitter"
            )
            .set(
                "spark.hadoop.mapreduce.outputcommitter.factory.scheme.s3a",
                "org.apache.hadoop.fs.s3a.commit.S3ACommitterFactory"
            )
            // Optimised based on https://spark.apache.org/docs/latest/cloud-integration.html
            // and https://hadoop.apache.org/docs/r3.1.2/hadoop-aws/tools/hadoop-aws/committers.html
            .set("spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version", "2")
            // Set Java temp directory to writable location
            .set(
                "spark.driver.extraJavaOptions",
                "-XX:+UseG1GC -XX:+UseContainerSupport -Xlog:logging=debug"
            )
            .set("spark.executor.extraJavaOptions", "-Xlog:logging=debug")
            // Set all possible Spark temp directories to our writable location
            .set("spark.local.dir", sparkTempDir)
            .set("spark.sql.warehouse.dir", warehouseDir.absolutePath)
            .set("spark.sql.artifact.dir", "/tmp/spark-artifacts")
            .set("spark.sql.artifact.root.dir", "/tmp/spark-artifacts")
            .set("spark.sql.streaming.checkpointLocation", checkpointsDir.absolutePath)
            .set("spark.worker.cleanup.enabled", "true")
            .set("spark.worker.cleanup.interval", "3600")
            .set("spark.worker.cleanup.appDataTtl", "86400")
            // Set JVM temp directory
            .set("java.io.tmpdir", "/tmp")

        // Log all Spark configurations
        println("\n=== Spark Configuration ===")
        conf.getAll().forEach { pair ->
            println("${pair._1} = ${pair._2}")
        }
        println("==========================\n")

        // Log environment variables that might affect Spark
        logImportantEnvVars()

        return conf
    }

    /**
     * Logs important environment variables that might affect Spark
     */
    private fun logImportantEnvVars() {
        println("\n=== Important Environment Variables ===")
        val envVars = System.getenv()
        listOf(
            "SPARK_HOME", "HADOOP_HOME", "JAVA_HOME", "PATH",
            "SPARK_LOCAL_DIRS", "SPARK_WORKER_DIR", "SPARK_LOG_DIR"
        ).forEach { varName ->
            println("$varName = ${envVars[varName] ?: "Not set"}")
        }
        println("======================================\n")
    }

    /**
     * Logs information about a directory including its existence and permissions
     */
    private fun logDirectoryInfo(directory: File) {
        println("Directory: ${directory.absolutePath}")
        println("  Exists: ${directory.exists()}")
        if (directory.exists()) {
            println("  Is directory: ${directory.isDirectory}")
            println("  Is writable: ${directory.canWrite()}")
            println("  Is readable: ${directory.canRead()}")
            println("  Is executable: ${directory.canExecute()}")
            println("  Total space: ${directory.totalSpace / (1024 * 1024)} MB")
            println("  Usable space: ${directory.usableSpace / (1024 * 1024)} MB")
        }

        // List contents if it's a directory
        if (directory.exists() && directory.isDirectory) {
            println("  Contents (first 10):")
            directory.listFiles()?.take(10)?.forEach { file ->
                println("    - ${file.name} (${if (file.isDirectory) "dir" else "file"})")
            }
            val totalFiles = directory.listFiles()?.size ?: 0
            if (totalFiles > 10) {
                println("    ... and ${totalFiles - 10} more")
            }
        }
    }
}