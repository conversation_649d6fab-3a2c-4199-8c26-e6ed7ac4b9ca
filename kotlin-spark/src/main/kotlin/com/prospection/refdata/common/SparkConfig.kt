package com.prospection.refdata.common

import org.apache.spark.SparkConf
import java.io.File

object SparkConfig {
    private val ENVIRONMENTS = arrayOf("int", "uat", "prd")
    private val BUCKET_COMMONS = mapOf(
        "spark.hadoop.fs.s3a.bucket.pd-au-%s-common.endpoint" to "s3.ap-southeast-2.amazonaws.com",
        "spark.hadoop.fs.s3a.bucket.pd-jp-%s-common.endpoint" to "s3.ap-northeast-1.amazonaws.com",
        "spark.hadoop.fs.s3a.bucket.pd-us-%s-common.endpoint" to "s3.us-west-2.amazonaws.com",
    )

    /*
     * This method is used to get the Spark configuration for the cloud environment.
     */
    fun getCloudSparkConfig(): SparkConf {
        val sparkConfig = getDefaultSparkConfig()
            .set(
                "spark.hadoop.fs.s3a.aws.credentials.provider",
                "software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider"
            )
            .set("spark.hadoop.fs.s3a.endpoint", "s3.ap-southeast-2.amazonaws.com")
            .set(
                "spark.hadoop.fs.s3a.bucket.prospection-data-lake-ap-northeast-1.endpoint",
                "s3.ap-northeast-1.amazonaws.com"
            )
            .set("spark.hadoop.fs.s3a.bucket.prospection-data-lake-us-west-2.endpoint", "s3.us-west-2.amazonaws.com")

        ENVIRONMENTS.forEach { env ->
            BUCKET_COMMONS.forEach { (bucket, region) ->
                sparkConfig.set(String.format(bucket, env), region)
            }
        }

        return sparkConfig
    }

    fun getDefaultSparkConfig(): SparkConf {
        // WORKAROUND for Spark 4.0 ArtifactManager bug (SPARK-52396)
        // This forces ArtifactManager to use system temp directory instead of working directory
        // See: https://github.com/apache/spark/pull/51083
        applyArtifactManagerWorkaround()

        return SparkConf()
            .set("spark.master", "local[*]")
            .set("spark.hadoop.fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem")
            .set("spark.hadoop.fs.s3a.fast.upload", "true")
            .set("spark.hadoop.fs.s3a.fast.upload.buffer", "bytebuffer")
            .set("spark.testing.memory", "471859200")
            // Optimised based on https://spark.apache.org/docs/latest/cloud-integration.html
            // and https://hadoop.apache.org/docs/r3.1.2/hadoop-aws/tools/hadoop-aws/committers.html
            .set("spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version", "2")
            .set("spark.hadoop.mapreduce.fileoutputcommitter.cleanup-failures.ignored", "true")
            .set("spark.hadoop.parquet.enable.summary-metadata", "false")
            .set("spark.sql.parquet.mergeSchema", "false")
            .set("spark.sql.parquet.filterPushdown", "true")
            .set("spark.sql.hive.metastorePartitionPruning", "true")
            .set("spark.hadoop.fs.s3a.commiter.staging.conflict-mode", "replace")
            .set("spark.hadoop.fs.s3a.committer.name", "magic")
            .set(
                "spark.sql.sources.commitProtocolClass",
                "org.apache.spark.internal.io.cloud.PathOutputCommitProtocol"
            )
            .set(
                "spark.sql.parquet.output.committer.class",
                "org.apache.spark.internal.io.cloud.BindingParquetOutputCommitter"
            )
            .set(
                "spark.hadoop.mapreduce.outputcommitter.factory.scheme.s3a",
                "org.apache.hadoop.fs.s3a.commit.S3ACommitterFactory"
            )
    }

    /**
     * WORKAROUND for Spark 4.0 ArtifactManager bug (SPARK-52396)
     *
     * The ArtifactManager in Spark 4.0 tries to create directories in the working directory
     * instead of the system temp directory, causing permission issues in Docker environments.
     *
     * This workaround forces the ArtifactManager to use the system temp directory by:
     * 1. Setting system properties that influence temp directory creation
     * 2. Ensuring the working directory is set to a writable location
     *
     * Fixed in Spark 4.0.1: https://github.com/apache/spark/pull/51083
     */
    private fun applyArtifactManagerWorkaround() {
        println("SparkConfig: Applying ArtifactManager workaround for Spark 4.0 bug (SPARK-52396)")

        // Set system properties to influence temp directory creation
        System.setProperty("java.io.tmpdir", "/tmp")

        // Try to change working directory to a writable location
        try {
            System.setProperty("user.dir", "/tmp")
            println("SparkConfig: Changed working directory to /tmp")
        } catch (e: Exception) {
            println("SparkConfig: Warning - Could not change working directory: ${e.message}")
        }

        // Create artifacts directory in temp location to ensure it exists
        val artifactsDir = File("/tmp/spark-artifacts")
        if (artifactsDir.mkdirs()) {
            println("SparkConfig: Created artifacts directory: ${artifactsDir.absolutePath}")
        }

        // Set permissions on the artifacts directory
        try {
            artifactsDir.setWritable(true, false)
            artifactsDir.setReadable(true, false)
            artifactsDir.setExecutable(true, false)
            println("SparkConfig: Set permissions on artifacts directory")
        } catch (e: Exception) {
            println("SparkConfig: Warning - Could not set permissions on artifacts directory: ${e.message}")
        }

        println("SparkConfig: ArtifactManager workaround applied")
    }
}