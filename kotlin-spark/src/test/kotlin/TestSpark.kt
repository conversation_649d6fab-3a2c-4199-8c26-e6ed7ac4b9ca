import org.apache.spark.sql.SparkSession

object TestSpark {
    val spark: SparkSession = SparkSession.builder()
        .appName("Test Spark App")
        .master("local[1]")
        .config("spark.sql.shuffle.partitions", 1)
        .config("spark.default.parallelism", 1)
        .config("spark.sql.adaptive.enabled", "false")
        .config("spark.shuffle.compress", "false")
        .config("spark.broadcast.compress", "false")
        .config("spark.ui.enabled", "false")
        // Configure temp directories for containerized test environments
        .config("spark.local.dir", "/tmp/spark")
        .config("spark.sql.warehouse.dir", "/tmp/spark-warehouse")
        .config("spark.sql.artifact.dir", "/tmp/spark-artifacts")
        .getOrCreate()
        .also { it.sparkContext().setLogLevel("WARN") }
}