import org.apache.spark.sql.SparkSession
import java.io.File

object TestSpark {
    init {
        // WORKAROUND for Spark 4.0 ArtifactManager bug (SPARK-52396)
        // Apply this before any Spark initialization
        applyArtifactManagerWorkaround()
    }

    val spark: SparkSession = SparkSession.builder()
        .appName("Test Spark App")
        .master("local[1]")
        .config("spark.sql.shuffle.partitions", 1)
        .config("spark.default.parallelism", 1)
        .config("spark.sql.adaptive.enabled", "false")
        .config("spark.shuffle.compress", "false")
        .config("spark.broadcast.compress", "false")
        .config("spark.ui.enabled", "false")
        // Configure temp directories for containerized test environments
        .config("spark.local.dir", "/tmp/spark")
        .config("spark.sql.warehouse.dir", "/tmp/spark-warehouse")
        .config("spark.sql.artifact.dir", "/tmp/spark-artifacts")
        .config("spark.sql.artifact.root.dir", "/tmp/spark-artifacts")
        .getOrCreate()
        .also { it.sparkContext().setLogLevel("WARN") }

    /**
     * WORKAROUND for Spark 4.0 ArtifactManager bug (SPARK-52396)
     * Fixed in Spark 4.0.1: https://github.com/apache/spark/pull/51083
     */
    private fun applyArtifactManagerWorkaround() {
        // Set system properties to influence temp directory creation
        System.setProperty("java.io.tmpdir", "/tmp")
        System.setProperty("user.dir", "/tmp")

        // Create artifacts directory in temp location to ensure it exists
        val artifactsDir = File("/tmp/spark-artifacts")
        artifactsDir.mkdirs()
        artifactsDir.setWritable(true, false)
        artifactsDir.setReadable(true, false)
        artifactsDir.setExecutable(true, false)
    }
}