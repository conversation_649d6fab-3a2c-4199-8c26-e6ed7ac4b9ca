# Spark 4.0 ArtifactManager Workaround

## Problem Description

Apache Spark 4.0 introduced a bug in the `ArtifactManager` class (SPARK-52396) where it attempts to create temporary directories in the current working directory instead of the system temp directory. This causes permission issues in Docker environments where the working directory is read-only.

### Error Symptoms
```
java.io.IOException: Failed to create a temp directory (under artifacts) after 10 attempts!
	at org.apache.spark.network.util.JavaUtils.createDirectory(JavaUtils.java:411)
	at org.apache.spark.util.SparkFileUtils.createDirectory(SparkFileUtils.scala:95)
	at org.apache.spark.util.SparkFileUtils.createDirectory$(SparkFileUtils.scala:94)
	at org.apache.spark.util.Utils$.createDirectory(Utils.scala:99)
	at org.apache.spark.util.Utils$.createTempDir(Utils.scala:249)
	at org.apache.spark.sql.artifact.ArtifactManager$.artifactRootDirectory$lzycompute(ArtifactManager.scala:468)
```

## Root Cause

The issue is in `ArtifactManager.scala` line 468:
```scala
Utils.createTempDir(ARTIFACT_DIRECTORY_PREFIX).toPath
```

This should be:
```scala
Utils.createTempDir(namePrefix = ARTIFACT_DIRECTORY_PREFIX).toPath
```

## Official Fix Status

- **Fixed in**: Apache Spark 4.0.1 and 4.1.0
- **GitHub PR**: https://github.com/apache/spark/pull/51083
- **Expected Release**: August/September 2025 for Spark 4.0.1

## Workaround Implementation

We have implemented a workaround that forces the ArtifactManager to use the system temp directory by setting system properties before Spark initialization.

### Files Modified

1. **kotlin-spark/src/main/kotlin/com/prospection/refdata/common/SparkConfig.kt**
   - Added `applyArtifactManagerWorkaround()` function
   - Called before Spark configuration setup

2. **lambdas-spark/src/main/kotlin/com/prospection/refdata/config/SparkConfiguration.kt**
   - Added `applyArtifactManagerWorkaround()` function
   - Called before Spark session creation

3. **kotlin-spark/src/test/kotlin/TestSpark.kt**
   - Added workaround in static initializer block

### Workaround Details

The workaround performs the following actions:

1. **Set System Properties**:
   - `java.io.tmpdir = "/tmp"`
   - `user.dir = "/tmp"`

2. **Create Artifacts Directory**:
   - Creates `/tmp/spark-artifacts` directory
   - Sets proper permissions (readable, writable, executable)

3. **Apply Before Spark Initialization**:
   - Must be called before any Spark session creation
   - Influences JVM temp directory behavior

### Code Example

```kotlin
private fun applyArtifactManagerWorkaround() {
    println("Applying ArtifactManager workaround for Spark 4.0 bug (SPARK-52396)")
    
    // Set system properties to influence temp directory creation
    System.setProperty("java.io.tmpdir", "/tmp")
    System.setProperty("user.dir", "/tmp")
    
    // Create artifacts directory in temp location to ensure it exists
    val artifactsDir = File("/tmp/spark-artifacts")
    artifactsDir.mkdirs()
    artifactsDir.setWritable(true, false)
    artifactsDir.setReadable(true, false)
    artifactsDir.setExecutable(true, false)
    
    println("ArtifactManager workaround applied")
}
```

## Verification

We have created a comprehensive test suite to verify the workaround:

**Test File**: `kotlin-spark/src/test/kotlin/com/prospection/refdata/common/ArtifactManagerWorkaroundTest.kt`

**Test Coverage**:
- Spark session initialization without permission errors
- DataFrame operations that may trigger ArtifactManager
- SQL operations that may trigger ArtifactManager
- Verification of artifacts directory configuration

**Test Results**: ✅ All tests pass successfully

## Usage Instructions

### For New Spark Applications

1. Call the workaround function before creating any Spark sessions:
```kotlin
applyArtifactManagerWorkaround()
val spark = SparkSession.builder()...
```

### For Existing Applications

The workaround is already integrated into:
- `SparkConfig.getDefaultSparkConfig()`
- `SparkConfiguration.spark` (Lambda)
- `TestSpark` (Test environment)

No additional changes are required for existing code.

## Docker Configuration

Ensure your Dockerfile creates the necessary directories:

```dockerfile
# Create all necessary directories for Spark
RUN mkdir -p /tmp/spark-temp && \
    mkdir -p /tmp/spark-temp/warehouse && \
    mkdir -p /tmp/spark-temp/checkpoints && \
    mkdir -p /tmp/spark-temp/work && \
    mkdir -p /tmp/spark-temp/spark-artifacts && \
    # Set permissions
    chmod -R 777 /tmp/spark-temp
```

## Migration Path

### When Spark 4.0.1 is Released

1. Upgrade to Spark 4.0.1 or later
2. Remove the workaround function calls
3. Remove the workaround functions themselves
4. Test thoroughly to ensure the official fix works

### Monitoring

- Monitor Spark release notes for 4.0.1 availability
- Test with 4.0.1 release candidates when available
- Keep the workaround code until official fix is verified

## Performance Impact

The workaround has minimal performance impact:
- Only affects Spark initialization time
- No runtime performance degradation
- Uses standard system temp directory (recommended practice)

## Compatibility

- **Spark Version**: 4.0.0 (workaround required)
- **Java Version**: Compatible with all supported Java versions
- **Docker**: Works in all Docker environments
- **AWS Lambda**: Fully compatible with Lambda execution environment

## References

- **Spark JIRA**: [SPARK-52396](https://issues.apache.org/jira/browse/SPARK-52396)
- **GitHub PR**: https://github.com/apache/spark/pull/51083
- **Spark Documentation**: https://spark.apache.org/docs/latest/
