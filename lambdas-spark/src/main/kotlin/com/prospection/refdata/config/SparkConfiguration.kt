package com.prospection.refdata.config

import com.prospection.refdata.common.SparkConfig
import org.apache.spark.sql.SparkSession
import java.io.File

object SparkConfiguration {
    // Do not use by lazy here to utilise AWS Lambda 10s initialisation CPU bust
    val spark: SparkSession = run {
        val numCores = System.getenv("NUM_CORES")?.toInt() ?: 1
        println("\n=== Initializing Spark Session ===")
        println("Available vCPUs: ${Runtime.getRuntime().availableProcessors()}")
        println("Cores to use: $numCores")

        // Define base temporary directory for Spark in Lambda environment
        val lambdaTempDir = "/tmp/spark-temp"

        // WORKAROUND for Spark 4.0 ArtifactManager bug (SPARK-52396)
        // Apply this before any Spark initialization
        applyArtifactManagerWorkaround()

        // Ensure the temp directory exists and log its info
        val tempDir = File(lambdaTempDir)
        val created = tempDir.mkdirs()
        println("Spark temp directory: ${tempDir.absolutePath} (created: $created)")
        logDirectoryInfo(tempDir)

        // Create subdirectories
        val warehouseDir = File("$lambdaTempDir/warehouse")
        val checkpointsDir = File("$lambdaTempDir/checkpoints")
        val artifactsDir = File("$lambdaTempDir/spark-artifacts")
        warehouseDir.mkdirs()
        checkpointsDir.mkdirs()
        artifactsDir.mkdirs()

        println("Warehouse directory: ${warehouseDir.absolutePath}")
        println("Checkpoints directory: ${checkpointsDir.absolutePath}")
        println("Artifacts directory: ${artifactsDir.absolutePath}")

        // Get base config and add Lambda-specific settings
        val sparkConfig = SparkConfig.getCloudSparkConfig()
            .set("spark.master", "local[$numCores]")
            .set("spark.sql.shuffle.partitions", numCores.toString())
            .set("spark.default.parallelism", numCores.toString())
            .set("spark.sql.adaptive.enabled", "false")
            .set("spark.shuffle.compress", "false")
            .set("spark.broadcast.compress", "false")
            .set("spark.ui.enabled", "false")
            // must set for lambda to work
            .set("spark.driver.bindAddress", "127.0.0.1")
            // Set temp directories to writable locations in Lambda
            .set("spark.local.dir", lambdaTempDir)
            .set("spark.sql.warehouse.dir", warehouseDir.absolutePath)
            .set("spark.sql.artifact.dir", artifactsDir.absolutePath)
            .set("spark.sql.artifact.root.dir", artifactsDir.absolutePath)
            .set("spark.sql.streaming.checkpointLocation", checkpointsDir.absolutePath)
            .set("spark.worker.cleanup.enabled", "true")
            .set("spark.worker.cleanup.interval", "3600")
            .set("spark.worker.cleanup.appDataTtl", "3600") // 1 hour TTL for temp files

        // Log environment variables that might affect Spark
        logImportantEnvVars()

        // Log all Spark configurations
        println("\n=== Final Spark Configuration ===")
        sparkConfig.getAll().forEach { pair ->
            println("${pair._1} = ${pair._2}")
        }
        println("================================\n")
        val spark = SparkSession.builder()
            .appName("pd-ref-data-service-v2")
            .config(sparkConfig)
            .orCreate

        spark.sparkContext().setLogLevel("WARN")
        println("=== Spark Session Initialization Complete ===\n")
        spark
    }

    /**
     * Logs important environment variables that might affect Spark
     */
    private fun logImportantEnvVars() {
        println("\n=== Important Environment Variables ===")
        val envVars = System.getenv()
        listOf(
            "SPARK_HOME", "HADOOP_HOME", "JAVA_HOME", "PATH",
            "SPARK_LOCAL_DIRS", "SPARK_WORKER_DIR", "SPARK_LOG_DIR",
            "AWS_LAMBDA_FUNCTION_NAME", "AWS_REGION", "AWS_EXECUTION_ENV"
        ).forEach { varName ->
            println("$varName = ${envVars[varName] ?: "Not set"}")
        }
        println("======================================\n")
    }

    /**
     * WORKAROUND for Spark 4.0 ArtifactManager bug (SPARK-52396)
     *
     * The ArtifactManager in Spark 4.0 tries to create directories in the working directory
     * instead of the system temp directory, causing permission issues in Docker environments.
     *
     * This workaround forces the ArtifactManager to use the system temp directory by:
     * 1. Setting system properties that influence temp directory creation
     * 2. Ensuring the working directory is set to a writable location
     *
     * Fixed in Spark 4.0.1: https://github.com/apache/spark/pull/51083
     */
    private fun applyArtifactManagerWorkaround() {
        println("SparkConfiguration: Applying ArtifactManager workaround for Spark 4.0 bug (SPARK-52396)")

        // Set system properties to influence temp directory creation
        System.setProperty("java.io.tmpdir", "/tmp")

        // Try to change working directory to a writable location
        try {
            System.setProperty("user.dir", "/tmp")
            println("SparkConfiguration: Changed working directory to /tmp")
        } catch (e: Exception) {
            println("SparkConfiguration: Warning - Could not change working directory: ${e.message}")
        }

        // Create artifacts directory in temp location to ensure it exists
        val artifactsDir = File("/tmp/spark-artifacts")
        if (artifactsDir.mkdirs()) {
            println("SparkConfiguration: Created artifacts directory: ${artifactsDir.absolutePath}")
        }

        // Set permissions on the artifacts directory
        try {
            artifactsDir.setWritable(true, false)
            artifactsDir.setReadable(true, false)
            artifactsDir.setExecutable(true, false)
            println("SparkConfiguration: Set permissions on artifacts directory")
        } catch (e: Exception) {
            println("SparkConfiguration: Warning - Could not set permissions on artifacts directory: ${e.message}")
        }

        println("SparkConfiguration: ArtifactManager workaround applied")
    }

    /**
     * Logs information about a directory including its existence and permissions
     */
    private fun logDirectoryInfo(directory: File) {
        println("Directory: ${directory.absolutePath}")
        println("  Exists: ${directory.exists()}")
        if (directory.exists()) {
            println("  Is directory: ${directory.isDirectory}")
            println("  Is writable: ${directory.canWrite()}")
            println("  Is readable: ${directory.canRead()}")
            println("  Is executable: ${directory.canExecute()}")
            println("  Total space: ${directory.totalSpace / (1024 * 1024)} MB")
            println("  Usable space: ${directory.usableSpace / (1024 * 1024)} MB")
        }

        // List contents if it's a directory
        if (directory.exists() && directory.isDirectory) {
            println("  Contents (first 10):")
            directory.listFiles()?.take(10)?.forEach { file ->
                println("    - ${file.name} (${if (file.isDirectory) "dir" else "file"})")
            }
            val totalFiles = directory.listFiles()?.size ?: 0
            if (totalFiles > 10) {
                println("    ... and ${totalFiles - 10} more")
            }
        }
    }
}